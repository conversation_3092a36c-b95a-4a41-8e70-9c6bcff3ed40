<?php

namespace Modules\Organization\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Organization\Models\Domain;

class DomainSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create default domain
        Domain::updateOrCreate(
            ['hostname' => 'localhost'],
            [
                'name' => 'AutoPAY Development',
                'description' => 'Default development domain for AutoPAY application',
                'brand_name' => 'AutoPAY',
                'slogan' => 'Thanh toán tự động thông minh',
                'logo_url' => null,
                'favicon_url' => null,
                'theme_colors' => [
                    'primary' => '#3b82f6',
                    'secondary' => '#64748b',
                    'accent' => '#f59e0b',
                    'background' => '#ffffff',
                    'surface' => '#f8fafc',
                    'text' => '#1e293b',
                    'text_secondary' => '#64748b',
                ],
                'custom_css' => [],
                'meta_title' => 'AutoPAY - Thanh toán tự động thông minh',
                'meta_description' => 'Nền tảng thanh toán tự động hàng đầu Việt Nam',
                'meta_keywords' => 'thanh toán, tự động, autopay, fintech',
                'og_image_url' => null,
                'contact_info' => [
                    'email' => '<EMAIL>',
                    'phone' => '+84 123 456 789',
                    'address' => 'Hà Nội, Việt Nam',
                ],
                'custom_config' => [
                    'timezone' => 'Asia/Ho_Chi_Minh',
                    'date_format' => 'd/m/Y',
                    'currency_format' => 'VND',
                ],
                'is_active' => true,
            ]
        );

        // Create production domain example
        Domain::updateOrCreate(
            ['hostname' => 'app.autopay.vn'],
            [
                'name' => 'AutoPAY Production',
                'description' => 'Production domain for AutoPAY application',
                'brand_name' => 'AutoPAY',
                'slogan' => 'Thanh toán tự động thông minh',
                'logo_url' => 'https://app.autopay.vn/logo.png',
                'favicon_url' => 'https://app.autopay.vn/favicon.ico',
                'theme_colors' => [
                    'primary' => '#2563eb',
                    'secondary' => '#475569',
                    'accent' => '#dc2626',
                    'background' => '#ffffff',
                    'surface' => '#f1f5f9',
                    'text' => '#0f172a',
                    'text_secondary' => '#475569',
                ],
                'custom_css' => [],
                'meta_title' => 'AutoPAY - Nền tảng thanh toán tự động',
                'meta_description' => 'Giải pháp thanh toán tự động hàng đầu cho doanh nghiệp Việt Nam',
                'meta_keywords' => 'thanh toán tự động, fintech, autopay, vietnam',
                'og_image_url' => 'https://app.autopay.vn/og-image.png',
                'contact_info' => [
                    'email' => '<EMAIL>',
                    'phone' => '+84 24 1234 5678',
                    'address' => 'Tầng 10, Tòa nhà ABC, Hà Nội, Việt Nam',
                    'website' => 'https://autopay.vn',
                ],
                'custom_config' => [
                    'timezone' => 'Asia/Ho_Chi_Minh',
                    'date_format' => 'd/m/Y',
                    'currency_format' => 'VND',
                    'security_headers' => [
                        'X-Frame-Options' => 'DENY',
                        'X-Content-Type-Options' => 'nosniff',
                        'X-XSS-Protection' => '1; mode=block',
                    ],
                ],
                'is_active' => true,
            ]
        );

        // Create client domain example
        Domain::updateOrCreate(
            ['hostname' => 'client.autopay.vn'],
            [
                'name' => 'AutoPAY Client Portal',
                'description' => 'Client-specific domain with custom branding',
                'brand_name' => 'Client Portal',
                'slogan' => 'Quản lý thanh toán dễ dàng',
                'logo_url' => 'https://client.autopay.vn/client-logo.png',
                'favicon_url' => 'https://client.autopay.vn/client-favicon.ico',
                'theme_colors' => [
                    'primary' => '#059669',
                    'secondary' => '#6b7280',
                    'accent' => '#f59e0b',
                    'background' => '#ffffff',
                    'surface' => '#f9fafb',
                    'text' => '#111827',
                    'text_secondary' => '#6b7280',
                ],
                'custom_css' => [
                    '.header' => [
                        'background-color' => '#059669',
                        'color' => '#ffffff',
                    ],
                    '.sidebar' => [
                        'background-color' => '#f9fafb',
                        'border-right' => '1px solid #e5e7eb',
                    ],
                ],
                'meta_title' => 'Client Portal - AutoPAY',
                'meta_description' => 'Portal quản lý thanh toán dành cho khách hàng',
                'meta_keywords' => 'client portal, payment management, autopay',
                'og_image_url' => 'https://client.autopay.vn/og-image.png',
                'contact_info' => [
                    'email' => '<EMAIL>',
                    'phone' => '+84 123 456 789',
                ],
                'custom_config' => [
                    'timezone' => 'Asia/Ho_Chi_Minh',
                    'date_format' => 'd/m/Y',
                    'currency_format' => 'VND',
                    'publicPaths' => ['/login', '/register', '/forgot-password'],
                    'authPaths' => ['/login', '/register'],
                ],
                'is_active' => true,
            ]
        );
    }
}
