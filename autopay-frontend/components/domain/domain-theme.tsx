'use client'

import { useDomain } from '@/lib/hooks/use-domain'
import { useEffect } from 'react'

/**
 * Component to apply domain-specific theme styles
 * This component should be placed high in the component tree
 */
export function DomainTheme(): JSX.Element | null {
  const { config } = useDomain()

  useEffect(() => {
    if (config?.theme) {
      const root = document.documentElement

      // Apply theme colors as CSS custom properties
      Object.entries(config.theme).forEach(([key, value]) => {
        root.style.setProperty(`--domain-${key}`, value)

        // Also set as standard CSS variables for easier use
        switch (key) {
          case 'primary':
            root.style.setProperty('--primary', value)
            break
          case 'secondary':
            root.style.setProperty('--secondary', value)
            break
          case 'accent':
            root.style.setProperty('--accent', value)
            break
          case 'background':
            root.style.setProperty('--background', value)
            break
          case 'surface':
            root.style.setProperty('--card', value)
            break
          case 'text':
            root.style.setProperty('--foreground', value)
            break
          case 'text_secondary':
            root.style.setProperty('--muted-foreground', value)
            break
        }
      })
    }
  }, [config])

  return null
}
