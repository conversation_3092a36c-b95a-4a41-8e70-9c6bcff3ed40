export interface DomainConfig {
  id: string
  hostname: string
  name: string
  description?: string
  branding: {
    name: string
    slogan?: string
    logo_url?: string
    favicon_url?: string
  }
  theme: {
    primary: string
    secondary: string
    accent: string
    background: string
    surface: string
    text: string
    text_secondary: string
  }
  seo: {
    title: string
    description?: string
    keywords?: string
    og_image?: string
  }
  contact: Record<string, any>
  custom: Record<string, any>
  custom_css: Record<string, any>
}

export interface DomainContextType {
  config: DomainConfig | null
  isLoading: boolean
  error: string | null
  refreshConfig: () => Promise<void>
}
