import { Metadata } from 'next'

import PageHeading from '@/app/(panel)/common/components/page-heading'
import { Card, CardContent } from '@/components/ui/card'
import React from 'react'
import { SidebarNav } from './components/sidebar-nav'

export const metadata: Metadata = {
  title: 'Forms',
  description: 'Advanced form example using react-hook-form and Zod.',
}

const sidebarNavItems = [
  {
    title: 'C<PERSON><PERSON> hình',
    href: '/settings',
  },
  {
    title: 'T<PERSON><PERSON> khoản',
    href: '/settings/account',
  },
  {
    title: '<PERSON><PERSON> sơ công ty',
    href: '/settings/appearance',
  },
  {
    title: 'Thông báo',
    href: '/settings/notifications',
  },
  {
    title: 'Hiển thị',
    href: '/settings/display',
  },
]

interface SettingsLayoutProps {
  children: React.ReactNode
}

export default function SettingsLayout({ children }: SettingsLayoutProps) {
  return (
    <>
      <PageHeading
        title="Cấu hình hệ thống"
        description="Tùy chỉnh hệ thống phù hợp với nhu cầu của bạn."
        withSeparator={true}
      />
      <div className="flex flex-col space-y-8 lg:flex-row lg:space-y-0 lg:space-x-5">
        <aside className="md:-ml-1 md:w-52">
          <SidebarNav items={sidebarNavItems} />
        </aside>
        <Card className="flex-1">
          <CardContent className="px-6">{children}</CardContent>
        </Card>
      </div>
    </>
  )
}
